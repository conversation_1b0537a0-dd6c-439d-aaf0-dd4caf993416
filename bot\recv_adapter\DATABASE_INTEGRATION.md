# 数据库集成和商品匹配功能

本文档介绍如何使用新增的数据库读写功能和商品匹配解析器。

## 功能概述

### 1. 数据库连接模块 (`utils/database.py`)
- 提供MySQL数据库连接池管理
- 支持异步操作
- 自动处理连接的创建和释放
- 提供商品数据的CRUD操作
- 提供店铺数据的查询操作（支持通过群ID查询）

### 2. 商品匹配器增强 (`utils/product_matcher.py`)
- 支持从数据库动态加载商品数据
- 高性能的商品名称匹配算法
- 支持精确匹配、模糊匹配和系列表达式匹配
- 自动处理错别字和别名匹配

### 3. 消息处理器集成 (`handlers/message_handler.py`)
- 在文本消息处理中自动识别商品
- 将识别结果添加到转发消息中
- 支持实时商品匹配
- 在群消息处理中自动查询店铺信息
- 将店铺信息添加到转发消息中

## 数据库配置

数据库配置已添加到 `config.py` 中：

```python
# MySQL数据库配置
mysql_host: str = "rm-uf6psdjix97ed81r8.mysql.rds.aliyuncs.com"
mysql_port: int = 3306
mysql_user: str = "bot"
mysql_password: str = "yaoboan19990312!"
mysql_database: str = "yba_ppmt"
mysql_charset: str = "utf8mb4"
```

## 数据库表结构

商品表 `products` 的结构：

```sql
CREATE TABLE `products` (
    `product_id` int NOT NULL COMMENT '商品ID',
    `original_name` varchar(64) NOT NULL COMMENT '原始名称',
    `nick_name` varchar(64) NULL COMMENT '显示昵称',
    `product_img` varchar(255) NULL COMMENT '商品图片',
    `match_names` json NULL COMMENT '匹配名称列表',
    `series_key` varchar(64) NULL COMMENT '系列名称',
    `series_variant` varchar(64) NULL COMMENT '系列品类',
    `cdn_info` json NULL COMMENT 'CDN信息',
    `cdn_update` datetime NULL COMMENT 'CDN更新时间', 
    `created_at` datetime NOT NULL COMMENT '条目创建时间',
    PRIMARY KEY (`product_id`)
) ENGINE=InnoDB DEFAULT CHARACTER SET=utf8mb4;
```

## 使用方法

### 1. 基本使用

```python
from utils.database import init_database, close_database
from utils.product_matcher import init_matcher_from_database, match_products

async def main():
    # 初始化数据库
    await init_database()
    
    # 从数据库初始化商品匹配器
    await init_matcher_from_database()
    
    # 进行商品匹配
    text = "我想要DIMOO心动特调系列的盲盒"
    matches = match_products(text, max_edits=1)
    
    for match in matches:
        print(f"商品: {match['商品名称']} (ID: {match['商品ID']})")
    
    # 关闭数据库连接
    await close_database()
```

### 2. 数据库操作

```python
from utils.database import get_product_database

async def get_products():
    product_db = get_product_database()
    
    # 获取所有商品
    all_products = await product_db.get_all_products()
    
    # 根据ID获取特定商品
    product = await product_db.get_product_by_id(123)
    
    return all_products, product
```

### 3. 商品匹配

```python
from utils.product_matcher import match_products

# 精确匹配
matches = match_products("DIMOO心动特调系列", max_edits=0)

# 模糊匹配（允许1个错别字）
matches = match_products("DIMO心动特调系列", max_edits=1)

# 系列表达式匹配
matches = match_products("DIMOO心动特调系列-软脸毛绒钥匙扣")
```

## 匹配结果格式

商品匹配返回的结果格式：

```python
[
    {
        "商品ID": 123,
        "商品名称": "DIMOO心动特调系列盲盒",
        "匹配信息": {
            "匹配模式": "dimoo心动特调系列",
            "原始文本中的位置": [5, 15],
            "错字信息列表": []
        }
    }
]
```

## 集成到主程序

主程序 `main.py` 已自动集成数据库和商品匹配功能：

1. 启动时自动初始化数据库连接
2. 从数据库加载商品数据到匹配器
3. 在消息处理中自动进行商品匹配
4. 停止时自动关闭数据库连接

## 测试和示例

### 运行测试

```bash
# 测试数据库连接和商品匹配
python test_database_integration.py

# 查看使用示例
python example_usage.py
```

### 测试内容

1. **数据库连接测试**：验证数据库连接和商品数据获取
2. **商品匹配测试**：测试各种匹配场景
3. **特定商品查询**：测试根据ID查询商品
4. **集成测试**：模拟实际使用场景

## 依赖项

新增的依赖项已添加到 `requirements.txt`：

```
aiomysql==0.2.0
```

安装依赖：

```bash
pip install -r requirements.txt
```

## 注意事项

1. **数据库连接**：确保数据库服务器可访问，网络连接稳定
2. **性能优化**：商品匹配器在启动时一次性加载所有数据，适合商品数量不是特别大的场景
3. **错误处理**：所有数据库操作都包含异常处理，确保程序稳定性
4. **资源管理**：使用连接池管理数据库连接，自动处理连接的创建和释放

## 扩展功能

### 1. 添加新的匹配规则

可以在 `product_matcher.py` 中扩展匹配算法：

```python
def custom_match_rule(text: str, products: List[ProductInfo]) -> List[MatchCandidate]:
    # 自定义匹配逻辑
    pass
```

### 2. 数据库操作扩展

可以在 `database.py` 中添加更多数据库操作：

```python
async def update_product(self, product_id: int, updates: Dict[str, Any]) -> bool:
    # 更新商品信息
    pass

async def search_products(self, keyword: str) -> List[Dict[str, Any]]:
    # 搜索商品
    pass
```

### 3. 缓存优化

可以添加Redis缓存来提高性能：

```python
# 缓存商品数据
# 定期刷新匹配器
# 缓存匹配结果
```

## 故障排除

### 常见问题

1. **数据库连接失败**
   - 检查网络连接
   - 验证数据库配置信息
   - 确认数据库服务状态

2. **商品匹配无结果**
   - 检查数据库中是否有商品数据
   - 验证匹配器是否正确初始化
   - 调整匹配参数（如编辑距离）

3. **性能问题**
   - 检查数据库查询性能
   - 考虑添加索引
   - 优化匹配算法

### 日志调试

启用详细日志来调试问题：

```python
import logging
logging.getLogger('aiomysql').setLevel(logging.DEBUG)
```
