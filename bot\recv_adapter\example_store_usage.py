"""
店铺匹配功能使用示例
"""
import asyncio
import sys
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.append(str(Path(__file__).parent))

from loguru import logger
from utils.database import init_database, close_database
from utils.store_matcher import (
    init_store_matcher_from_database, 
    get_store_by_group_id, 
    match_stores_by_name
)


async def example_basic_usage():
    """基本使用示例"""
    logger.info("=== 店铺匹配功能基本使用示例 ===")
    
    # 1. 初始化数据库
    await init_database()
    logger.info("数据库已初始化")
    
    # 2. 从数据库初始化店铺匹配器
    await init_store_matcher_from_database()
    logger.info("店铺匹配器已初始化")
    
    # 3. 通过group_id查找店铺（高性能内存索引）
    test_group_id = "example_group@chatroom"  # 替换为实际的group_id
    store = get_store_by_group_id(test_group_id)
    
    if store:
        logger.info(f"找到店铺: {store['店铺名称']} (ID: {store['店铺ID']})")
        logger.info(f"地区: {store.get('地区', '')}")
        logger.info(f"开业状态: {'开业' if store.get('开业状态') else '暂停营业'}")
        logger.info(f"群组列表: {store.get('群组列表', [])}")
    else:
        logger.info(f"未找到group_id为 {test_group_id} 的店铺")
    
    # 4. 通过店铺名称匹配
    test_store_name = "泡泡玛特"  # 替换为实际的店铺名称
    matches = match_stores_by_name(test_store_name, max_edits=1)
    
    logger.info(f"\n搜索店铺名称: {test_store_name}")
    logger.info(f"匹配结果: {len(matches)} 个店铺")
    
    for match in matches:
        logger.info(f"  - {match['店铺名称']} (ID: {match['店铺ID']})")
        logger.info(f"    地区: {match.get('地区', '')}")
        logger.info(f"    匹配模式: {match['匹配信息']['匹配模式']}")
        logger.info(f"    置信度: {match['匹配信息']['置信度']:.2f}")


async def example_message_processing():
    """模拟消息处理中的店铺匹配"""
    logger.info("\n=== 模拟消息处理中的店铺匹配 ===")
    
    # 模拟群消息
    class MockMessage:
        def __init__(self, group_id, content):
            self.group_id = group_id
            self.content = content
    
    # 模拟不同的群消息
    test_messages = [
        MockMessage("store1_group@chatroom", "今天有新品上架"),
        MockMessage("store2_group@chatroom", "请问有DIMOO系列吗？"),
        MockMessage("unknown_group@chatroom", "这是什么店铺？"),
    ]
    
    for message in test_messages:
        logger.info(f"\n处理群消息: {message.group_id}")
        logger.info(f"消息内容: {message.content}")
        
        # 通过group_id查找店铺
        store = get_store_by_group_id(message.group_id)
        if store:
            logger.info(f"✅ 识别店铺: {store['店铺名称']} (ID: {store['店铺ID']})")
            logger.info(f"   地区: {store.get('地区', '')}")
            
            # 这里可以添加业务逻辑，比如：
            # - 记录店铺的消息统计
            # - 根据店铺信息调整商品匹配策略
            # - 发送店铺相关的自动回复等
        else:
            logger.info("❌ 未识别到对应店铺")
            
            # 尝试从消息内容中匹配店铺名称
            store_matches = match_stores_by_name(message.content, max_edits=1)
            if store_matches:
                logger.info(f"💡 从消息内容中识别到可能的店铺:")
                for match in store_matches[:2]:  # 只显示前2个匹配结果
                    logger.info(f"   - {match['店铺名称']} (置信度: {match['匹配信息']['置信度']:.2f})")


async def example_performance_comparison():
    """性能对比示例"""
    logger.info("\n=== 性能对比示例 ===")
    
    import time
    from utils.database import get_store_database
    
    test_group_id = "example_group@chatroom"  # 替换为实际存在的group_id
    
    # 测试内存索引查询
    start_time = time.time()
    for _ in range(1000):
        store = get_store_by_group_id(test_group_id)
    memory_time = time.time() - start_time
    
    # 测试数据库查询
    store_db = get_store_database()
    start_time = time.time()
    for _ in range(10):  # 减少次数，因为数据库查询较慢
        store = await store_db.get_store_by_group_id(test_group_id)
    db_time = time.time() - start_time
    
    logger.info(f"内存索引查询 1000 次: {memory_time:.4f} 秒")
    logger.info(f"数据库查询 10 次: {db_time:.4f} 秒")
    logger.info(f"内存索引平均每次: {memory_time/1000*1000:.4f} 毫秒")
    logger.info(f"数据库查询平均每次: {db_time/10*1000:.4f} 毫秒")
    
    if memory_time > 0:
        speedup = (db_time/10) / (memory_time/1000)
        logger.info(f"性能提升: {speedup:.1f} 倍")


async def example_fuzzy_matching():
    """模糊匹配示例"""
    logger.info("\n=== 模糊匹配示例 ===")
    
    # 测试不同的模糊匹配场景
    test_cases = [
        ("泡泡玛特", "精确匹配"),
        ("泡泡马特", "错别字匹配"),
        ("POP MART", "英文名匹配"),
        ("pop mart", "大小写不敏感"),
        ("泡泡", "部分匹配"),
        ("玛特店", "部分匹配"),
    ]
    
    for test_name, description in test_cases:
        logger.info(f"\n测试: {test_name} ({description})")
        matches = match_stores_by_name(test_name, max_edits=2)
        
        if matches:
            logger.info(f"找到 {len(matches)} 个匹配:")
            for match in matches[:3]:  # 只显示前3个结果
                confidence = match['匹配信息']['置信度']
                match_mode = match['匹配信息']['匹配模式']
                logger.info(f"  - {match['店铺名称']} (置信度: {confidence:.2f}, {match_mode})")
        else:
            logger.info("未找到匹配的店铺")


async def main():
    """主函数"""
    logger.info("店铺匹配功能使用示例")
    
    try:
        # 基本使用示例
        await example_basic_usage()
        
        # 消息处理示例
        await example_message_processing()
        
        # 性能对比示例
        await example_performance_comparison()
        
        # 模糊匹配示例
        await example_fuzzy_matching()
        
        logger.info("\n✅ 所有示例运行完成")
        
    except Exception as e:
        logger.error(f"❌ 运行示例时发生错误: {e}")
    
    finally:
        # 关闭数据库连接
        await close_database()
        logger.info("数据库连接已关闭")


if __name__ == "__main__":
    asyncio.run(main())
