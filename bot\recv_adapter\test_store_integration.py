"""
店铺数据库集成测试
"""
import asyncio
import sys
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.append(str(Path(__file__).parent))

from loguru import logger
from utils.database import init_database, close_database, get_store_database


async def test_store_database_connection():
    """测试店铺数据库连接"""
    logger.info("测试店铺数据库连接...")
    
    try:
        # 初始化数据库
        await init_database()
        logger.info("✅ 数据库连接成功")
        
        # 获取店铺数据库实例
        store_db = get_store_database()
        if not store_db:
            logger.error("❌ 无法获取店铺数据库实例")
            return False
            
        # 测试获取所有店铺数据
        stores = await store_db.get_all_stores()
        logger.info(f"✅ 成功获取 {len(stores)} 个店铺")
        
        # 显示前几个店铺的信息
        for i, store in enumerate(stores[:3]):
            logger.info(f"店铺 {i+1}: ID={store['store_id']}, 名称={store['store_name']}, "
                       f"群组={store.get('group_ids', [])}, 地区={store.get('location', '')}")
            
        return True
        
    except Exception as e:
        logger.error(f"❌ 店铺数据库连接测试失败: {e}")
        return False


async def test_store_query_by_group_id():
    """测试通过群ID查询店铺"""
    logger.info("测试通过群ID查询店铺...")
    
    try:
        store_db = get_store_database()
        if not store_db:
            logger.error("❌ 店铺数据库未初始化")
            return False
        
        # 首先获取所有店铺，找一个有群ID的店铺进行测试
        stores = await store_db.get_all_stores()
        test_group_id = None
        test_store_name = None
        
        for store in stores:
            group_ids = store.get('group_ids', [])
            if group_ids:
                test_group_id = group_ids[0]
                test_store_name = store['store_name']
                break
        
        if not test_group_id:
            logger.warning("⚠️ 没有找到包含群ID的店铺，无法测试群ID查询功能")
            return True
        
        logger.info(f"使用测试群ID: {test_group_id}")
        
        # 通过群ID查询店铺
        store = await store_db.get_store_by_group_id(test_group_id)
        
        if store:
            logger.info(f"✅ 成功通过群ID查询到店铺:")
            logger.info(f"  - 店铺ID: {store['store_id']}")
            logger.info(f"  - 店铺名称: {store['store_name']}")
            logger.info(f"  - 地区: {store.get('location', '未设置')}")
            logger.info(f"  - 开业状态: {'开业' if store.get('opening', True) else '暂停营业'}")
            logger.info(f"  - 群组列表: {store.get('group_ids', [])}")
            
            # 验证查询结果是否正确
            if store['store_name'] == test_store_name:
                logger.info("✅ 查询结果验证正确")
            else:
                logger.error(f"❌ 查询结果验证失败: 期望 {test_store_name}, 实际 {store['store_name']}")
                return False
        else:
            logger.error(f"❌ 通过群ID {test_group_id} 未找到店铺")
            return False
            
        # 测试不存在的群ID
        fake_group_id = "fake_group_id_12345"
        fake_store = await store_db.get_store_by_group_id(fake_group_id)
        if fake_store is None:
            logger.info(f"✅ 正确处理不存在的群ID: {fake_group_id}")
        else:
            logger.error(f"❌ 不存在的群ID返回了结果: {fake_store}")
            return False
            
        return True
        
    except Exception as e:
        logger.error(f"❌ 群ID查询测试失败: {e}")
        return False


async def test_store_integration():
    """完整的店铺集成测试"""
    logger.info("=== 店铺数据库集成测试 ===")
    
    success = True
    
    # 测试数据库连接
    if not await test_store_database_connection():
        success = False
    
    # 测试群ID查询
    if not await test_store_query_by_group_id():
        success = False
    
    # 关闭数据库连接
    await close_database()
    
    if success:
        logger.info("🎉 所有店铺集成测试通过!")
    else:
        logger.error("❌ 部分测试失败")
    
    return success


async def main():
    """主函数"""
    try:
        await test_store_integration()
    except KeyboardInterrupt:
        logger.info("测试被用户中断")
    except Exception as e:
        logger.error(f"测试过程中发生错误: {e}")
    finally:
        # 确保数据库连接被关闭
        try:
            await close_database()
        except:
            pass


if __name__ == "__main__":
    asyncio.run(main())
