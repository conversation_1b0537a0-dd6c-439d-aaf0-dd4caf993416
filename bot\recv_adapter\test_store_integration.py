"""
店铺数据库集成和匹配功能测试
"""
import asyncio
import sys
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.append(str(Path(__file__).parent))

from loguru import logger
from utils.database import init_database, close_database, get_store_database
from utils.store_matcher import (
    init_store_matcher_from_database, 
    get_store_by_group_id, 
    match_stores_by_name,
    get_global_store_matcher
)


async def test_database_connection():
    """测试数据库连接和店铺数据获取"""
    logger.info("=== 测试数据库连接和店铺数据获取 ===")
    
    try:
        # 初始化数据库
        await init_database()
        logger.info("✅ 数据库连接成功")
        
        # 获取店铺数据库实例
        store_db = get_store_database()
        if not store_db:
            logger.error("❌ 无法获取店铺数据库实例")
            return False
            
        # 测试获取所有店铺数据
        stores = await store_db.get_all_stores()
        logger.info(f"✅ 成功获取 {len(stores)} 个店铺")
        
        # 显示前几个店铺的信息
        for i, store in enumerate(stores[:3]):
            logger.info(f"店铺 {i+1}: ID={store['id']}, 名称={store['name']}, "
                       f"群组={store.get('group_ids', [])}, 地区={store.get('location', '')}")
            
        return True
        
    except Exception as e:
        logger.error(f"❌ 数据库连接测试失败: {e}")
        return False


async def test_store_matcher():
    """测试店铺匹配器"""
    logger.info("=== 测试店铺匹配器 ===")
    
    try:
        # 从数据库初始化匹配器
        await init_store_matcher_from_database()
        logger.info("✅ 店铺匹配器初始化成功")
        
        # 获取匹配器实例
        matcher = get_global_store_matcher()
        if not matcher:
            logger.error("❌ 无法获取店铺匹配器实例")
            return False
        
        # 测试group_id查找（需要替换为实际的group_id）
        test_group_ids = [
            "test_group_1@chatroom",
            "example_group@chatroom",
            "nonexistent_group@chatroom"
        ]
        
        logger.info("\n--- 测试group_id查找 ---")
        for group_id in test_group_ids:
            store = get_store_by_group_id(group_id)
            if store:
                logger.info(f"✅ 群 {group_id} -> 店铺: {store['店铺名称']} (ID: {store['店铺ID']})")
            else:
                logger.info(f"❌ 群 {group_id} -> 未找到对应店铺")
        
        # 测试店铺名称匹配
        test_store_names = [
            "泡泡玛特",
            "POP MART",
            "潮玩店",
            "玩具店",
            "不存在的店铺名称"
        ]
        
        logger.info("\n--- 测试店铺名称匹配 ---")
        for store_name in test_store_names:
            logger.info(f"\n测试店铺名称: {store_name}")
            
            # 进行店铺匹配
            matches = match_stores_by_name(store_name, max_edits=1)
            
            if matches:
                logger.info(f"✅ 找到 {len(matches)} 个匹配店铺:")
                for match in matches:
                    store_id = match.get("店铺ID")
                    store_name_matched = match.get("店铺名称")
                    location = match.get("地区", "")
                    opening = match.get("开业状态", True)
                    match_info = match.get("匹配信息", {})
                    match_mode = match_info.get("匹配模式", "")
                    confidence = match_info.get("置信度", 0)
                    
                    logger.info(f"  - 店铺: {store_name_matched} (ID: {store_id})")
                    logger.info(f"    地区: {location}, 开业: {opening}")
                    logger.info(f"    匹配模式: {match_mode}, 置信度: {confidence:.2f}")
            else:
                logger.info("❌ 未找到匹配的店铺")
                
        return True
        
    except Exception as e:
        logger.error(f"❌ 店铺匹配器测试失败: {e}")
        return False


async def test_group_id_database_query():
    """测试通过group_id直接查询数据库"""
    logger.info("=== 测试group_id数据库查询 ===")
    
    try:
        store_db = get_store_database()
        if not store_db:
            logger.error("❌ 无法获取店铺数据库实例")
            return False
        
        # 测试group_id查询（需要替换为实际的group_id）
        test_group_ids = [
            "test_group_1@chatroom",
            "example_group@chatroom"
        ]
        
        for group_id in test_group_ids:
            logger.info(f"\n测试group_id: {group_id}")
            store = await store_db.get_store_by_group_id(group_id)
            
            if store:
                logger.info(f"✅ 找到店铺: {store['name']} (ID: {store['id']})")
                logger.info(f"  地区: {store.get('location', '')}")
                logger.info(f"  开业状态: {store.get('opening', True)}")
                logger.info(f"  群组列表: {store.get('group_ids', [])}")
            else:
                logger.info(f"❌ 未找到group_id对应的店铺")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ group_id数据库查询测试失败: {e}")
        return False


async def test_performance():
    """测试性能对比：内存索引 vs 数据库查询"""
    logger.info("=== 测试性能对比 ===")
    
    try:
        import time
        
        # 测试group_id
        test_group_id = "test_group_1@chatroom"  # 需要替换为实际存在的group_id
        
        # 测试内存索引查询性能
        start_time = time.time()
        for _ in range(1000):
            store = get_store_by_group_id(test_group_id)
        memory_time = time.time() - start_time
        
        # 测试数据库查询性能
        store_db = get_store_database()
        start_time = time.time()
        for _ in range(100):  # 减少次数，因为数据库查询较慢
            store = await store_db.get_store_by_group_id(test_group_id)
        db_time = time.time() - start_time
        
        logger.info(f"内存索引查询 1000 次耗时: {memory_time:.4f} 秒")
        logger.info(f"数据库查询 100 次耗时: {db_time:.4f} 秒")
        logger.info(f"内存索引平均每次: {memory_time/1000*1000:.4f} 毫秒")
        logger.info(f"数据库查询平均每次: {db_time/100*1000:.4f} 毫秒")
        logger.info(f"性能提升: {(db_time/100)/(memory_time/1000):.1f} 倍")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 性能测试失败: {e}")
        return False


async def main():
    """主测试函数"""
    logger.info("开始店铺数据库集成和匹配功能测试")
    
    try:
        # 1. 测试数据库连接
        success = await test_database_connection()
        if not success:
            return
        
        # 2. 测试店铺匹配器
        success = await test_store_matcher()
        if not success:
            return
        
        # 3. 测试group_id数据库查询
        success = await test_group_id_database_query()
        if not success:
            return
        
        # 4. 测试性能对比
        success = await test_performance()
        if not success:
            return
        
        logger.info("✅ 所有测试完成")
        
    except Exception as e:
        logger.error(f"❌ 测试过程中发生错误: {e}")
    
    finally:
        # 关闭数据库连接
        await close_database()
        logger.info("数据库连接已关闭")


if __name__ == "__main__":
    asyncio.run(main())
